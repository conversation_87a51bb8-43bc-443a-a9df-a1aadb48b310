#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库初始化脚本测试
"""
import os
import sys
import tempfile
import shutil
from flask import Flask

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_database_init():
    """测试数据库初始化"""
    print("开始测试数据库初始化...")
    
    # 创建临时目录用于测试
    temp_dir = tempfile.mkdtemp()
    original_cwd = os.getcwd()
    
    try:
        # 切换到临时目录
        os.chdir(temp_dir)
        
        # 复制必要的文件
        project_files = ['models.py', 'config.py', 'init_database.py']
        for file in project_files:
            src_path = os.path.join(original_cwd, file)
            if os.path.exists(src_path):
                shutil.copy2(src_path, temp_dir)
                print(f"  复制文件: {file}")
        
        # 创建utils目录（如果需要）
        os.makedirs('utils', exist_ok=True)
        
        # 导入并测试初始化脚本
        from init_database import create_app, verify_database_structure
        
        print("  创建测试应用...")
        app = create_app()
        
        with app.app_context():
            from models import db
            
            print("  创建数据库表...")
            db.create_all()
            
            print("  验证数据库结构...")
            verify_database_structure()
            
            print("  ✓ 数据库初始化测试通过")
            
    except Exception as e:
        print(f"  ✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 恢复原始工作目录
        os.chdir(original_cwd)
        
        # 清理临时目录
        try:
            shutil.rmtree(temp_dir)
        except:
            pass
    
    return True

if __name__ == '__main__':
    success = test_database_init()
    if success:
        print("✅ 所有测试通过")
        sys.exit(0)
    else:
        print("❌ 测试失败")
        sys.exit(1)
